"use client"

import type React from "react"
import { useState } from "react"
import { useRouter } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { User, Lock } from "lucide-react"

export default function LoginPage() {
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [error, setError] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setIsLoading(true)
    setError("")

    // Simulate loading delay
    await new Promise((resolve) => setTimeout(resolve, 500))

    if (password === "123456") {
      // Store login state in localStorage
      localStorage.setItem("isLoggedIn", "true")
      localStorage.setItem("username", username)
      router.push("/dashboard")
    } else {
      setError("密码错误")
    }

    setIsLoading(false)
  }

  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden bg-gradient-to-br from-purple-400 via-purple-300 to-blue-300">
      <div className="absolute inset-0 overflow-hidden">
        <div className="absolute top-20 left-20 w-16 h-16 bg-blue-500/30 rounded-lg transform rotate-12 animate-pulse"></div>
        <div className="absolute top-40 right-32 w-12 h-12 bg-purple-500/40 rounded-full animate-bounce"></div>
        <div className="absolute bottom-32 left-16 w-20 h-20 bg-blue-400/25 rounded-xl transform -rotate-45"></div>
        <div className="absolute top-60 left-1/3 w-8 h-8 bg-purple-600/35 rounded-full"></div>
        <div className="absolute bottom-20 right-20 w-14 h-14 bg-blue-300/30 rounded-lg transform rotate-45"></div>
        <div className="absolute top-32 right-1/4 w-10 h-10 bg-purple-400/40 rounded-full animate-pulse"></div>
        <div className="absolute bottom-40 left-1/2 w-6 h-6 bg-blue-500/35 rounded-full"></div>
        <div className="absolute top-1/2 right-16 w-18 h-18 bg-purple-300/30 rounded-xl transform rotate-12"></div>
      </div>

      <Card className="w-full max-w-md relative z-10 bg-white/95 backdrop-blur-sm border-0 shadow-2xl">
        <CardHeader className="text-center pb-6">
          <CardTitle className="text-3xl font-bold text-gray-900 mb-2">Agent 管理系统登录</CardTitle>
        </CardHeader>
        <CardContent className="px-8 pb-8">
          <form onSubmit={handleLogin} className="space-y-6">
            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <User className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                id="username"
                type="text"
                placeholder="用户名"
                value={username}
                onChange={(e) => setUsername(e.target.value)}
                className="pl-10 h-12 bg-gray-50/80 border-gray-200 rounded-xl text-gray-900 placeholder:text-gray-500"
                required
              />
            </div>

            <div className="relative">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Lock className="h-5 w-5 text-gray-400" />
              </div>
              <Input
                id="password"
                type="password"
                placeholder="密码"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className="pl-10 h-12 bg-gray-50/80 border-gray-200 rounded-xl text-gray-900 placeholder:text-gray-500"
                required
              />
            </div>

            {error && (
              <Alert variant="destructive" className="rounded-xl">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <Button
              type="submit"
              className="w-full h-12 bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white font-semibold rounded-xl shadow-lg transition-all duration-200 transform hover:scale-[1.02]"
              disabled={isLoading}
            >
              {isLoading ? "登录中..." : "登录"}
            </Button>

            <div className="text-center">
              <span className="text-gray-400 text-sm">or</span>
            </div>

            <Button
              type="button"
              variant="outline"
              className="w-full h-12 border-gray-200 text-gray-600 rounded-xl hover:bg-gray-50 bg-transparent"
            >
              微信客服
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
