"use client"

import type React from "react"

import { useEffect, useState } from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import {
  LogOut,
  User,
  Menu,
  X,
  Activity,
  ChevronLeft,
  ChevronRight,
  Users,
  Loader2,
  AlertCircle,
  Home,
  Brain,
  BarChart3,
  LineChart,
  ChevronDown,
} from "lucide-react"

// Enhanced mock data for realistic demonstration
const agents = [
  {
    id: "agent1",
    name: "转销专家",
    url: "https://example.com/agent1",
    type: "客服",
    status: "在线",
    lastActive: "2分钟前",
    tasksCompleted: 127,
    successRate: 98.5,
  },
  {
    id: "agent2",
    name: "产品专家",
    url: "https://example.com/agent2",
    type: "分析",
    status: "忙碌",
    lastActive: "刚刚",
    tasksCompleted: 89,
    successRate: 96.2,
  },
]

const dashboardStats = [
  {
    title: "总代理",
    value: "4",
    subtitle: "3个在线",
    color: "bg-blue-500",
    trend: "+2",
    trendDirection: "up",
  },
  {
    title: "CPU使用率",
    value: "42.8%",
    subtitle: "平均负载",
    color: "bg-green-500",
    trend: "-5.2%",
    trendDirection: "down",
  },
  {
    title: "内存使用",
    value: "67.3%",
    subtitle: "8.2GB / 12GB",
    color: "bg-purple-500",
    trend: "+3.1%",
    trendDirection: "up",
  },
  {
    title: "今日任务",
    value: "445",
    subtitle: "已完成",
    color: "bg-orange-500",
    trend: "+28",
    trendDirection: "up",
  },
]

const agentActivity = [
  {
    id: 1,
    agent: "智能客服助手",
    action: "处理用户咨询",
    status: "已完成",
    time: "刚刚",
    color: "bg-green-500",
    duration: "2分钟",
  },
  {
    id: 2,
    agent: "数据分析专家",
    action: "生成销售报告",
    status: "进行中",
    time: "3分钟前",
    color: "bg-blue-500",
    duration: "预计5分钟",
  },
  {
    id: 3,
    agent: "内容创作助理",
    action: "撰写产品描述",
    status: "已完成",
    time: "5分钟前",
    color: "bg-green-500",
    duration: "8分钟",
  },
  {
    id: 4,
    agent: "智能客服助手",
    action: "更新知识库",
    status: "已完成",
    time: "8分钟前",
    color: "bg-green-500",
    duration: "3分钟",
  },
  {
    id: 5,
    agent: "代码审查机器人",
    action: "代码质量检查",
    status: "等待中",
    time: "12分钟前",
    color: "bg-yellow-500",
    duration: "待处理",
  },
]

// Performance metrics for charts
const performanceData = {
  systemLoad: [
    { time: "00:00", cpu: 35, memory: 62, network: 45 },
    { time: "04:00", cpu: 28, memory: 58, network: 38 },
    { time: "08:00", cpu: 42, memory: 65, network: 52 },
    { time: "12:00", cpu: 48, memory: 71, network: 61 },
    { time: "16:00", cpu: 38, memory: 67, network: 47 },
    { time: "20:00", cpu: 33, memory: 63, network: 42 },
  ],
  agentPerformance: [
    { name: "智能客服助手", completed: 127, success: 98.5, avgTime: 3.2 },
    { name: "数据分析专家", completed: 89, success: 96.2, avgTime: 12.5 },
    { name: "内容创作助理", completed: 156, success: 94.8, avgTime: 8.7 },
    { name: "代码审查机器人", completed: 73, success: 99.1, avgTime: 15.3 },
  ],
  taskDistribution: [
    { category: "客服咨询", count: 185, percentage: 41.6 },
    { category: "数据分析", count: 89, percentage: 20.0 },
    { category: "内容创作", count: 156, percentage: 35.1 },
    { category: "代码审查", count: 15, percentage: 3.3 },
  ],
}

export default function DashboardPage() {
  const [selectedView, setSelectedView] = useState<string>("home")
  const [username, setUsername] = useState("")
  const [sidebarCollapsed, setSidebarCollapsed] = useState(true) // Default to collapsed state
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false)
  const [touchStartX, setTouchStartX] = useState<number | null>(null)
  const [touchCurrentX, setTouchCurrentX] = useState<number | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [iframeError, setIframeError] = useState(false)
  const [expandedMenus, setExpandedMenus] = useState<string[]>([]) // Track expanded submenus
  const router = useRouter()

  useEffect(() => {
    const isLoggedIn = localStorage.getItem("isLoggedIn")
    const storedUsername = localStorage.getItem("username")

    if (!isLoggedIn) {
      router.push("/")
      return
    }

    if (storedUsername) {
      setUsername(storedUsername)
    }
  }, [router])

  // Enhanced mobile menu accessibility and keyboard support
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape" && mobileMenuOpen) {
        setMobileMenuOpen(false)
      }
    }

    const handleResize = () => {
      // Close mobile menu when switching to desktop view
      if (window.innerWidth >= 1024 && mobileMenuOpen) {
        setMobileMenuOpen(false)
      }
    }

    document.addEventListener("keydown", handleKeyDown)
    window.addEventListener("resize", handleResize)

    return () => {
      document.removeEventListener("keydown", handleKeyDown)
      window.removeEventListener("resize", handleResize)
    }
  }, [mobileMenuOpen])

  const handleLogout = () => {
    localStorage.removeItem("isLoggedIn")
    localStorage.removeItem("username")
    router.push("/")
  }

  const toggleSidebar = () => {
    setSidebarCollapsed(!sidebarCollapsed)
  }

  const toggleMobileMenu = () => {
    setMobileMenuOpen(!mobileMenuOpen)
  }

  // Enhanced touch handling for mobile menu
  const handleTouchStart = (e: React.TouchEvent) => {
    setTouchStartX(e.touches[0].clientX)
    setTouchCurrentX(e.touches[0].clientX)
  }

  const handleTouchMove = (e: React.TouchEvent) => {
    if (touchStartX === null) return
    setTouchCurrentX(e.touches[0].clientX)
  }

  const handleTouchEnd = () => {
    if (touchStartX === null || touchCurrentX === null) return

    const deltaX = touchCurrentX - touchStartX
    const threshold = 50 // Minimum swipe distance

    // Swipe right to open menu (when closed)
    if (deltaX > threshold && !mobileMenuOpen) {
      setMobileMenuOpen(true)
    }
    // Swipe left to close menu (when open)
    else if (deltaX < -threshold && mobileMenuOpen) {
      setMobileMenuOpen(false)
    }

    setTouchStartX(null)
    setTouchCurrentX(null)
  }

  // Close mobile menu when clicking outside
  const handleBackdropClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    setMobileMenuOpen(false)
  }

  const handleViewSelect = (view: string) => {
    // Renamed from handleAgentSelect to handleViewSelect
    if (view === "home") {
      setSelectedView("home")
      setMobileMenuOpen(false)
      return
    }

    setIsLoading(true)
    setIframeError(false)
    setSelectedView(view)
    setMobileMenuOpen(false)
    setTimeout(() => setIsLoading(false), 1000)
  }

  const toggleSubmenu = (menuId: string) => {
    setExpandedMenus((prev) => (prev.includes(menuId) ? prev.filter((id) => id !== menuId) : [...prev, menuId]))
  }

  return (
    <div className="min-h-screen bg-slate-50 flex">
      {/* Enhanced mobile menu backdrop with smooth fade animation */}
      <div
        className={`
          fixed inset-0 z-40 lg:hidden transition-all duration-300 ease-in-out
          ${mobileMenuOpen ? "bg-black bg-opacity-50 backdrop-blur-sm visible" : "bg-transparent invisible"}
        `}
        onClick={handleBackdropClick}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      />

      <aside
        className={`
          ${mobileMenuOpen ? "translate-x-0 shadow-2xl" : "-translate-x-full shadow-none"}
          lg:translate-x-0 lg:shadow-none
          fixed lg:relative
          z-50 lg:z-auto
          bg-slate-900
          border-r border-slate-700
          flex flex-col
          transition-all duration-300 ease-out
          ${sidebarCollapsed ? "lg:w-16" : "lg:w-80"}
          w-80
          h-screen
          lg:transform-none
        `}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
      >
        <div className={`border-b border-slate-700/50 ${sidebarCollapsed ? "p-2" : "p-3"}`}>
          <div className={`flex items-center mb-3 ${sidebarCollapsed ? "justify-center" : "justify-between"}`}>
            {!sidebarCollapsed && <h1 className="text-lg font-semibold text-white">Agent 管理系统</h1>}
            <div className="flex items-center gap-1">
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleSidebar}
                className="hidden lg:flex text-slate-400 hover:bg-slate-800/80 hover:text-white rounded-lg transition-all duration-200"
              >
                {sidebarCollapsed ? <ChevronRight className="h-4 w-4" /> : <ChevronLeft className="h-4 w-4" />}
              </Button>
              <Button
                variant="ghost"
                size="sm"
                onClick={toggleMobileMenu}
                className="lg:hidden text-slate-400 hover:bg-slate-800/60 hover:text-white rounded-lg transition-all duration-200 hover:scale-105 active:scale-95"
              >
                <div className="transition-transform duration-200 hover:rotate-90">
                  <X className="h-4 w-4" />
                </div>
              </Button>
            </div>
          </div>

          {sidebarCollapsed ? (
            <div className="flex justify-center mb-3 group relative">
              <div className="w-8 h-8 bg-blue-600 rounded-lg flex items-center justify-center cursor-pointer hover:bg-blue-700 transition-all duration-200 hover:scale-105">
                <User className="h-4 w-4 text-white" />
              </div>
              {/* Tooltip for user info */}
              <div className="absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none">
                <div className="bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl px-3 py-2 whitespace-nowrap before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95 overflow-hidden">
                  <p className="text-sm font-medium text-white">{username || "手系 Agent"}</p>
                  <p className="text-xs text-slate-400">管理员</p>
                </div>
              </div>
            </div>
          ) : (
            <div className="flex items-center gap-2 p-2.5 bg-slate-800/60 rounded-lg mb-3 border border-slate-700/30">
              <div className="w-7 h-7 bg-blue-600 rounded-lg flex items-center justify-center">
                <User className="h-3.5 w-3.5 text-white" />
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-sm font-medium text-white truncate">{username || "手系 Agent"}</p>
                <p className="text-xs text-slate-400">管理员</p>
              </div>
            </div>
          )}
        </div>

        <nav className={`flex-1 ${sidebarCollapsed ? "p-2 overflow-visible" : "p-3 overflow-y-auto"}`}>
          <div className="space-y-1">
            {/* Dashboard Home */}
            <div className="group relative">
              <button
                onClick={() => handleViewSelect("home")}
                className={`
                  w-full flex items-center rounded-lg text-sm font-medium
                  transition-all duration-200 min-h-[40px]
                  ${sidebarCollapsed ? "justify-center px-2 py-2.5" : "gap-2.5 px-2.5 py-2.5"}
                  ${
                    selectedView === "home"
                      ? "bg-blue-600 text-white shadow-md border border-blue-500/30"
                      : "text-slate-300 hover:bg-slate-800/60 hover:text-white"
                  }
                `}
              >
                <Home className="h-5 w-5 flex-shrink-0" />
                {!sidebarCollapsed && <span>首页</span>}
                {!sidebarCollapsed && selectedView === "home" && (
                  <div className="ml-auto w-1.5 h-1.5 bg-white rounded-full" />
                )}
              </button>

              {/* Tooltip for collapsed state */}
              {sidebarCollapsed && (
                <div className="absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto">
                  <div className="bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl px-3 py-2 whitespace-nowrap before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95 overflow-hidden">
                    <p className="text-sm font-medium text-white">首页</p>
                  </div>
                </div>
              )}
            </div>

            {/* AI Agents Section */}
            <div className="group relative">
              {sidebarCollapsed ? (
                /* Collapsed: Show section icon with hover submenu */
                <>
                  <div
                    className={`
                      w-full flex items-center justify-center rounded-lg text-sm font-medium
                      transition-all duration-200 min-h-[40px] px-2 py-2.5 cursor-default
                      ${
                        agents.some((agent) => selectedView === agent.id)
                          ? "bg-blue-600 text-white shadow-md border border-blue-500/30"
                          : "text-slate-300 hover:bg-slate-800/60 hover:text-white"
                      }
                    `}
                  >
                    <Brain className="h-5 w-5 flex-shrink-0" />
                  </div>

                  {/* Submenu tooltip */}
                  <div className="absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto transform translate-x-1 group-hover:translate-x-0">
                    <div className="bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl min-w-[220px] max-w-[280px] py-2 before:content-[''] before:absolute before:left-[-6px] before:top-4 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95 overflow-hidden">
                      <div className="px-3 py-2 border-b border-slate-700/50">
                        <h4 className="text-xs font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2">
                          <Brain className="h-3 w-3 text-blue-400" />
                          AI 专家
                        </h4>
                      </div>
                      <div className="py-1 max-h-[300px] overflow-y-auto overflow-x-hidden"
                           style={{ paddingRight: '1px' }}>
                        {agents.map((agent) => (
                          <button
                            key={agent.id}
                            onClick={() => handleViewSelect(agent.id)}
                            className={`
                              w-full flex items-center gap-2.5 px-3 py-2.5 text-sm font-medium
                              transition-all duration-200 hover:bg-slate-700/80 rounded-md mx-1
                              ${
                                selectedView === agent.id
                                  ? "bg-blue-600 text-white shadow-md"
                                  : "text-slate-300 hover:text-white"
                              }
                            `}
                          >
                            <div
                              className={`w-2 h-2 rounded-full flex-shrink-0 ${
                                agent.status === "在线"
                                  ? "bg-green-500"
                                  : agent.status === "忙碌"
                                    ? "bg-yellow-500"
                                    : "bg-gray-400"
                              }`}
                            ></div>
                            <span className="truncate">{agent.name}</span>
                            {selectedView === agent.id && <div className="ml-auto w-1.5 h-1.5 bg-white rounded-full" />}
                          </button>
                        ))}
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                /* Expanded: Show section header with expandable submenu */
                <>
                  <button
                    onClick={() => toggleSubmenu("ai-agents")}
                    className="w-full flex items-center justify-between px-2.5 py-2 text-xs font-semibold text-slate-400 uppercase tracking-wider hover:text-slate-300 transition-colors duration-200"
                  >
                    <div className="flex items-center gap-2">
                      <Brain className="h-3 w-3" />
                      AI 专家
                    </div>
                    <ChevronDown
                      className={`h-3 w-3 transition-transform duration-200 ${
                        expandedMenus.includes("ai-agents") ? "rotate-180" : ""
                      }`}
                    />
                  </button>

                  {/* Expandable agent list */}
                  <div
                    className={`overflow-hidden transition-all duration-300 ${
                      expandedMenus.includes("ai-agents") ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
                    }`}
                  >
                    <div className="space-y-0.5 mt-1">
                      {agents.map((agent) => (
                        <button
                          key={agent.id}
                          onClick={() => handleViewSelect(agent.id)}
                          className={`
                            w-full flex items-center rounded-lg text-sm font-medium
                            transition-all duration-200 min-h-[36px] gap-2.5 px-3 py-2 ml-1
                            ${
                              selectedView === agent.id
                                ? "bg-blue-600 text-white shadow-md border border-blue-500/30"
                                : "text-slate-300 hover:bg-slate-800/60 hover:text-white"
                            }
                          `}
                        >
                          <div
                            className={`w-2 h-2 rounded-full flex-shrink-0 ${
                              agent.status === "在线"
                                ? "bg-green-500"
                                : agent.status === "忙碌"
                                  ? "bg-yellow-500"
                                  : "bg-gray-400"
                            }`}
                          ></div>
                          <span className="truncate">{agent.name}</span>
                          {selectedView === agent.id && <div className="ml-auto w-1.5 h-1.5 bg-white rounded-full" />}
                        </button>
                      ))}
                    </div>
                  </div>
                </>
              )}
            </div>

            {/* Analytics Section */}
            <div className="group relative">
              {sidebarCollapsed ? (
                /* Collapsed: Show section icon with hover tooltip */
                <>
                  <div
                    className={`
                      w-full flex items-center justify-center rounded-lg text-sm font-medium
                      transition-all duration-200 min-h-[40px] px-2 py-2.5 cursor-default
                      ${
                        selectedView === "analytics"
                          ? "bg-blue-600 text-white shadow-md border border-blue-500/30"
                          : "text-slate-300 hover:bg-slate-800/60 hover:text-white"
                      }
                    `}
                  >
                    <BarChart3 className="h-5 w-5 flex-shrink-0" />
                  </div>

                  {/* Tooltip */}
                  <div className="absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto transform translate-x-1 group-hover:translate-x-0">
                    <div className="bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl min-w-[220px] max-w-[280px] py-2 before:content-[''] before:absolute before:left-[-6px] before:top-4 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95 overflow-hidden">
                      <div className="px-3 py-2 border-b border-slate-700/50">
                        <h4 className="text-xs font-semibold text-slate-300 uppercase tracking-wider flex items-center gap-2">
                          <BarChart3 className="h-3 w-3 text-purple-400" />
                          分析工具
                        </h4>
                      </div>
                      <div className="py-1" style={{ paddingRight: '1px' }}>
                        <button
                          onClick={() => handleViewSelect("analytics")}
                          className={`w-full flex items-center gap-2.5 px-3 py-2.5 text-sm font-medium transition-all duration-200 hover:bg-slate-700/80 rounded-md mx-1 ${selectedView === "analytics" ? "bg-blue-600 text-white shadow-md" : "text-slate-300 hover:text-white"}`}
                        >
                          <LineChart className="h-3.5 w-3.5 flex-shrink-0 text-purple-400" />
                          <span className="truncate">性能分析</span>
                          {selectedView === "analytics" && (
                            <div className="ml-auto w-1.5 h-1.5 bg-white rounded-full" />
                          )}
                        </button>
                      </div>
                    </div>
                  </div>
                </>
              ) : (
                /* Expanded: Show section header and analytics items */
                <>
                  <button
                    onClick={() => toggleSubmenu("analytics")}
                    className="w-full flex items-center justify-between px-2.5 py-2 text-xs font-semibold text-slate-400 uppercase tracking-wider hover:text-slate-300 transition-colors duration-200"
                  >
                    <div className="flex items-center gap-2">
                      <BarChart3 className="h-3 w-3" />
                      分析工具
                    </div>
                    <ChevronDown
                      className={`h-3 w-3 transition-transform duration-200 ${
                        expandedMenus.includes("analytics") ? "rotate-180" : ""
                      }`}
                    />
                  </button>

                  {/* Expandable analytics list */}
                  <div
                    className={`overflow-hidden transition-all duration-300 ${
                      expandedMenus.includes("analytics") ? "max-h-96 opacity-100" : "max-h-0 opacity-0"
                    }`}
                  >
                    <div className="space-y-0.5 mt-1">
                      <button
                        onClick={() => handleViewSelect("analytics")}
                        className={`
                          w-full flex items-center rounded-lg text-sm font-medium
                          transition-all duration-200 min-h-[36px] gap-2.5 px-3 py-2 ml-1
                          ${
                            selectedView === "analytics"
                              ? "bg-blue-600 text-white shadow-md border border-blue-500/30"
                              : "text-slate-300 hover:bg-slate-800/60 hover:text-white"
                          }
                        `}
                      >
                        <LineChart className="h-3.5 w-3.5 flex-shrink-0" />
                        <span className="truncate">性能分析</span>
                        {selectedView === "analytics" && <div className="ml-auto w-1.5 h-1.5 bg-white rounded-full" />}
                      </button>
                    </div>
                  </div>
                </>
              )}
            </div>
          </div>
        </nav>

        <div className={`border-t border-slate-700/50 ${sidebarCollapsed ? "p-2" : "p-3"}`}>
          <div className="group relative">
            <Button
              variant="ghost"
              onClick={handleLogout}
              className={`
                w-full text-slate-300 hover:bg-slate-800/60 hover:text-white rounded-lg
                transition-all duration-200 min-h-[40px]
                ${sidebarCollapsed ? "justify-center px-2 py-2" : "justify-start px-2.5 py-2"}
              `}
            >
              <LogOut className="h-5 w-5" />
              {!sidebarCollapsed && <span className="ml-2 text-sm">退出登录</span>}
            </Button>

            {/* Tooltip for collapsed state */}
            {sidebarCollapsed && (
              <div className="absolute left-full top-0 ml-3 opacity-0 invisible group-hover:opacity-100 group-hover:visible transition-all duration-300 ease-out z-50 pointer-events-none group-hover:pointer-events-auto">
                <div className="bg-slate-800/95 backdrop-blur-sm border border-slate-700/60 rounded-lg shadow-2xl px-3 py-2 whitespace-nowrap before:content-[''] before:absolute before:left-[-6px] before:top-3 before:w-0 before:h-0 before:border-t-[6px] before:border-b-[6px] before:border-r-[6px] before:border-transparent before:border-r-slate-800/95 overflow-hidden">
                  <p className="text-sm font-medium text-white">退出登录</p>
                </div>
              </div>
            )}
          </div>
        </div>
      </aside>

      <Button
        variant="ghost"
        size="sm"
        onClick={toggleMobileMenu}
        className={`
          lg:hidden fixed top-3 left-3 z-30
          bg-slate-900/95 backdrop-blur-sm text-white
          hover:bg-slate-800/90 active:bg-slate-700
          border border-slate-700/40 rounded-lg
          transition-all duration-200 ease-in-out
          hover:scale-105 active:scale-95
          shadow-md hover:shadow-lg
          min-h-[36px] min-w-[36px]
          ${mobileMenuOpen ? "bg-slate-800/90 scale-105" : ""}
        `}
        onTouchStart={handleTouchStart}
        onTouchMove={handleTouchMove}
        onTouchEnd={handleTouchEnd}
        title={mobileMenuOpen ? "关闭菜单" : "打开菜单"}
      >
        <div className={`transition-transform duration-200 ${mobileMenuOpen ? "rotate-90" : ""}`}>
          <Menu className="h-4 w-4" />
        </div>
      </Button>

      <main className="flex-1 p-3 lg:p-4 overflow-auto">
        {selectedView === "home" ? ( // Changed condition to check for home view
          <div className="space-y-4">
            {/* Welcome Header - Compact */}
            <div className="bg-white rounded-lg border border-slate-200 p-4">
              <h1 className="text-xl font-bold text-slate-900 mb-1">欢迎回来，{username || "管理员"}</h1>
              <p className="text-sm text-slate-600">AI Agent 管理中心概览</p>
            </div>

            {/* Dashboard Stats - Compact */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-3">
              {dashboardStats.map((stat, index) => (
                <Card key={index} className="bg-white border-slate-200 hover:shadow-lg transition-shadow">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-xs font-medium text-slate-600">{stat.title}</p>
                        <p className="text-lg font-bold text-slate-900 mt-1">{stat.value}</p>
                        <p className="text-xs text-slate-500">{stat.subtitle}</p>
                      </div>
                      <div className={`w-10 h-10 ${stat.color} rounded-lg flex items-center justify-center`}>
                        <div className="w-5 h-5 bg-white rounded opacity-80"></div>
                      </div>
                    </div>
                    {stat.trend && (
                      <div className="mt-2 flex items-center">
                        <span
                          className={`text-xs font-medium ${
                            stat.trendDirection === "up" ? "text-green-600" : "text-red-600"
                          }`}
                        >
                          {stat.trendDirection === "up" ? "↗" : "↘"} {stat.trend}
                        </span>
                        <span className="text-xs text-slate-500 ml-1">vs 昨天</span>
                      </div>
                    )}
                  </CardContent>
                </Card>
              ))}
            </div>

            {/* Consolidated Performance Dashboard - Responsive grid layout */}
            <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-3 gap-3">
              {/* Combined Agent Performance and Task Distribution */}
              <Card className="bg-white border-slate-200 hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <CardTitle className="text-slate-900 flex items-center gap-2 text-sm">
                    <BarChart3 className="h-4 w-4 text-blue-600" />
                    Agent 性能
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {performanceData.agentPerformance.slice(0, 3).map((agent, index) => (
                    <div key={index} className="flex items-center justify-between p-2 bg-slate-50 rounded-lg">
                      <div className="flex-1 min-w-0">
                        <p className="text-xs font-medium text-slate-900 truncate">{agent.name}</p>
                        <p className="text-xs text-slate-500">成功率: {agent.success}%</p>
                      </div>
                      <div className="text-right">
                        <p className="text-sm font-bold text-blue-600">{agent.completed}</p>
                        <p className="text-xs text-slate-500">任务</p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* System Load Chart - Compact */}
              <Card className="bg-white border-slate-200 hover:shadow-lg transition-shadow">
                <CardHeader className="pb-2">
                  <CardTitle className="text-slate-900 flex items-center gap-2 text-sm">
                    <LineChart className="h-4 w-4 text-purple-600" />
                    系统负载
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {performanceData.systemLoad.slice(-3).map((load, index) => (
                    <div key={index} className="space-y-1">
                      <div className="flex justify-between text-xs">
                        <span className="text-slate-600">{load.time}</span>
                        <span className="text-slate-900 font-medium">CPU: {load.cpu}%</span>
                      </div>
                      <div className="w-full bg-slate-200 rounded-full h-1.5">
                        <div
                          className="bg-purple-600 h-1.5 rounded-full transition-all duration-300"
                          style={{ width: `${load.cpu}%` }}
                        ></div>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>

              {/* Recent Activity - Compact */}
              <Card className="bg-white border-slate-200 md:col-span-2 xl:col-span-1">
                <CardHeader className="pb-2">
                  <CardTitle className="text-slate-900 flex items-center gap-2 text-sm">
                    <Activity className="h-4 w-4 text-orange-600" />
                    最近活动
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  {agentActivity.slice(0, 3).map((activity) => (
                    <div key={activity.id} className="flex items-center gap-2 p-2 bg-slate-50 rounded-lg">
                      <div className={`w-2 h-2 rounded-full ${activity.color}`}></div>
                      <div className="flex-1 min-w-0">
                        <p className="text-xs font-medium text-slate-900 truncate">{activity.agent}</p>
                        <p className="text-xs text-slate-600 truncate">{activity.action}</p>
                      </div>
                      <div className="text-right">
                        <p
                          className={`text-xs font-medium ${
                            activity.status === "已完成"
                              ? "text-green-600"
                              : activity.status === "进行中"
                                ? "text-blue-600"
                                : "text-yellow-600"
                          }`}
                        >
                          {activity.status}
                        </p>
                      </div>
                    </div>
                  ))}
                </CardContent>
              </Card>
            </div>

            {/* Agent Status - Single row for all agents with improved responsive layout */}
            <Card className="bg-white border-slate-200">
              <CardHeader className="pb-2">
                <CardTitle className="text-slate-900 flex items-center gap-2 text-sm">
                  <Users className="h-4 w-4 text-indigo-600" />
                  Agent 状态总览
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-2">
                  {agents.map((agent) => (
                    <div key={agent.id} className="flex items-center justify-between p-2 bg-slate-50 rounded-lg">
                      <div className="flex items-center gap-2">
                        <div
                          className={`w-2 h-2 rounded-full ${
                            agent.status === "在线"
                              ? "bg-green-500"
                              : agent.status === "忙碌"
                                ? "bg-yellow-500"
                                : "bg-gray-400"
                          }`}
                        ></div>
                        <div className="min-w-0">
                          <p className="text-xs font-medium text-slate-900 truncate">{agent.name}</p>
                          <p className="text-xs text-slate-500">{agent.type}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <p className="text-xs font-medium text-slate-900">{agent.tasksCompleted}</p>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        ) : (
          (() => {
            // Handle agent views
            const selectedAgent = agents.find((agent) => agent.id === selectedView)
            if (selectedAgent) {
              return (
                <Card className="h-full bg-white border-slate-200">
                  <CardContent className="h-full p-0">
                    {isLoading ? (
                      <div className="h-full flex items-center justify-center">
                        <div className="flex flex-col items-center gap-4">
                          <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                          <p className="text-slate-600">加载 {selectedAgent.name}...</p>
                        </div>
                      </div>
                    ) : iframeError ? (
                      <div className="h-full flex items-center justify-center">
                        <div className="flex flex-col items-center gap-4 text-center">
                          <AlertCircle className="h-12 w-12 text-red-500" />
                          <div>
                            <p className="text-slate-900 font-medium">加载失败</p>
                            <p className="text-slate-600 text-sm mt-1">无法加载 {selectedAgent.name}</p>
                          </div>
                          <Button onClick={() => handleViewSelect(selectedView)} variant="outline" size="sm">
                            重试
                          </Button>
                        </div>
                      </div>
                    ) : (
                      <iframe
                        src={selectedAgent.url}
                        className="w-full h-full border-0 rounded-lg"
                        title={selectedAgent.name}
                        onError={() => setIframeError(true)}
                        sandbox="allow-scripts allow-same-origin allow-forms"
                      />
                    )}
                  </CardContent>
                </Card>
              )
            }

            // Handle other menu items
            return (
              <Card className="h-full bg-white border-slate-200">
                <CardContent className="h-full p-0">
                  {isLoading ? (
                    <div className="h-full flex items-center justify-center">
                      <div className="flex flex-col items-center gap-4">
                        <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
                        <p className="text-slate-600">加载中...</p>
                      </div>
                    </div>
                  ) : (
                    <div className="h-full flex items-center justify-center">
                      <div className="text-center">
                        <Activity className="h-16 w-16 text-slate-400 mx-auto mb-4" />
                        <h2 className="text-xl font-semibold text-slate-900 mb-2">功能模块</h2>
                        <p className="text-slate-600">此功能正在开发中...</p>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )
          })()
        )}
      </main>
    </div>
  )
}